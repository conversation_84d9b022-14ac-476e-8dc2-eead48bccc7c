/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
      },
    ],
  },
  transpilePackages: [
    "framer-motion",
    "@sanity/ui",
    "@sanity/visual-editing"
  ],
  webpack: (config, { dev, isServer }) => {
    // Suppress warnings in development
    if (dev && !isServer) {
      config.stats = {
        ...config.stats,
        warningsFilter: [
          /React does not recognize.*disableTransition/,
          /styled-components/,
        ],
      };
    }
    return config;
  },
  /* config options here */
};

module.exports = nextConfig;
import { BasketIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

export const walletTopUpRequestType = defineType({
  name: 'walletTopUpRequest',
  title: 'Cüzdan Yükleme Talebi',
  type: 'document',
  icon: BasketIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'requestId',
      title: 'Talep ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `WTR-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Talep ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'user',
      title: 'Kullanıcı',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('<PERSON><PERSON><PERSON><PERSON>ı seçimi z<PERSON>du<PERSON>.'),
    }),
    defineField({
      name: 'amount',
      title: 'Yüklenecek Tutar',
      type: 'number',
      group: 'basic',
      validation: (Rule) => Rule.required().min(1).max(100000).error('Tutar 1 ile 100.000 arasında olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'paymentMethod',
      title: 'Ödeme Yöntemi',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Kredi Kartı', value: 'credit_card' },
          { title: 'Banka Havalesi', value: 'bank_transfer' },
          { title: 'Mobil Ödeme', value: 'mobile_payment' },
        ],
      },
      validation: (Rule) => Rule.required().error('Ödeme yöntemi zorunludur.'),
    }),
    defineField({
      name: 'paymentIntentId',
      title: 'Ödeme İşlem ID',
      type: 'string',
      group: 'basic',
      description: 'Stripe veya diğer ödeme sisteminden gelen işlem ID’si.',
    }),
    defineField({
      name: 'status',
      title: 'Durum',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'İşleniyor', value: 'processing' },
          { title: 'Onaylandı', value: 'approved' },
          { title: 'Reddedildi', value: 'rejected' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Durum zorunludur.'),
    }),
    defineField({
      name: 'reason',
      title: 'Reddetme Nedeni',
      type: 'text',
      group: 'status',
      description: 'Talep reddedildiyse, nedenini belirtin.',
      hidden: ({ document }) => document?.status !== 'rejected',
    }),
    defineField({
      name: 'createdAt',
      title: 'Talep Tarihi',
      type: 'datetime',
      group: 'status',
      readOnly: true,
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'approvedAt',
      title: 'Onay Tarihi',
      type: 'datetime',
      group: 'status',
      readOnly: true,
      hidden: ({ document }) => document?.status !== 'approved',
    }),
  ],
  preview: {
    select: {
      requestId: 'requestId',
      userName: 'user.name.tr',
      amount: 'amount',
      currency: 'currency',
      status: 'status',
      createdAt: 'createdAt',
    },
    prepare({ requestId, userName, amount, currency, status, createdAt }) {
      return {
        title: `Talep ${requestId || 'Bilinmiyor'}`,
        subtitle: `${userName || 'İsimsiz'} - ${amount ? `${amount} ${currency || 'TRY'}` : 'Tutar Yok'} - ${status || 'Durum Yok'} - ${new Date(createdAt).toLocaleDateString('tr-TR')}`,
        media: BasketIcon,
      };
    },
  },
});
import { BasketIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Helper function for address field
const defineAddressField = (title = 'Adres') =>
  defineField({
    name: 'shippingAddress',
    title,
    type: 'object',
    fields: [
      {
        name: 'street',
        title: 'Sokak/Cadde',
        type: 'string',
        validation: (Rule) => Rule.required().error('Sokak/Cadde zorunludur.'),
      },
      {
        name: 'city',
        title: 'Şehir',
        type: 'string',
        validation: (Rule) => Rule.required().error('Şehir zorunludur.'),
      },
      {
        name: 'state',
        title: 'İl/Eyalet',
        type: 'string',
      },
      {
        name: 'postalCode',
        title: 'Posta Kodu',
        type: 'string',
        validation: (Rule) => Rule.required().error('Posta kodu zorunludur.'),
      },
      {
        name: 'country',
        title: '<PERSON><PERSON><PERSON>',
        type: 'string',
        initialValue: 'Türkiye',
        validation: (Rule) => Rule.required().error('Ülke zorunludur.'),
      },
    ],
  });

export const orderType = defineType({
  name: 'order',
  title: 'Sipariş',
  type: 'document',
  icon: BasketIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'customer', title: 'Müşteri Bilgileri' },
    { name: 'items', title: 'Ürünler' },
    { name: 'payment', title: 'Ödeme' },
    { name: 'shipping', title: 'Kargo' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'orderNumber',
      title: 'Sipariş Numarası',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `ORD-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Sipariş numarası benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'customer',
      title: 'Müşteri Bilgileri',
      type: 'object',
      group: 'customer',
      fields: [
        {
          name: 'name',
          title: 'Ad Soyad',
          type: 'string',
          validation: (Rule) => Rule.required().error('Müşteri adı zorunludur.'),
        },
        {
          name: 'email',
          title: 'E-posta',
          type: 'string',
          validation: (Rule) => Rule.required().email().error('Geçerli bir e-posta adresi giriniz.'),
        },
        {
          name: 'clerkUserId',
          title: 'Clerk Kullanıcı ID',
          type: 'string',
          validation: (Rule) => Rule.required().error('Clerk kullanıcı ID zorunludur.'),
        },
      ],
      validation: (Rule) => Rule.required().error('Müşteri bilgileri zorunludur.'),
    }),
    defineField({
      name: 'orderNotes',
      title: 'Sipariş Notları',
      type: 'object',
      group: 'customer',
      fields: [
        {
          name: 'tr',
          title: 'Türkçe Not',
          type: 'text',
        },
        {
          name: 'en',
          title: 'İngilizce Not',
          type: 'text',
        },
      ],
      description: 'Müşteri tarafından eklenen özel notlar.',
    }),
    defineField({
      name: 'orderItems',
      title: 'Sipariş Kalemleri',
      type: 'array',
      group: 'items',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'product',
              title: 'Ürün',
              type: 'reference',
              to: [{ type: 'product' }],
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'quantity',
              title: 'Adet',
              type: 'number',
              validation: (Rule) => Rule.required().min(1),
            }),
            defineField({
              name: 'price',
              title: 'Birim Fiyat',
              type: 'number',
              validation: (Rule) => Rule.required().min(0),
            }),
            defineField({
              name: 'currency',
              title: 'Para Birimi',
              type: 'string',
              options: {
                list: [
                  { title: 'Türk Lirası (TRY)', value: 'TRY' },
                  { title: 'ABD Doları (USD)', value: 'USD' },
                  { title: 'Euro (EUR)', value: 'EUR' },
                ],
              },
              initialValue: 'TRY',
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'discount',
              title: 'İndirim Yüzdesi',
              type: 'number',
              validation: (Rule) => Rule.min(0).max(100).error('İndirim 0-100 arasında olmalıdır.'),
              description: 'Bu ürün için uygulanan indirim yüzdesi',
            }),
          ],
        }),
      ],
      validation: (Rule) => [
        Rule.min(1).error('En az bir ürün eklenmelidir.'),
        Rule.custom(async (items, context) => {
          if (!items || !Array.isArray(items)) return true;

          const client = context.getClient({ apiVersion: '2023-01-01' });

          for (const item of items) {
            if (item.product && item.product._ref && item.quantity) {
              try {
                const product = await client.fetch(
                  `*[_id == $id][0]{stock, isAvailable}`,
                  { id: item.product._ref }
                );

                if (!product) {
                  return `Ürün bulunamadı: ${item.product._ref}`;
                }

                if (!product.isAvailable) {
                  return `Ürün stokta mevcut değil: ${item.product._ref}`;
                }

                if (product.stock < item.quantity) {
                  return `Ürün için yeterli stok yok. Mevcut: ${product.stock}, İstenen: ${item.quantity}`;
                }
              } catch (error) {
                console.error('Stok kontrolü hatası:', error);
                return 'Stok kontrolü yapılamadı. Lütfen tekrar deneyin.';
              }
            }
          }
          return true;
        }),
      ],
    }),
    defineField({
      name: 'totalAmount',
      title: 'Toplam Tutar',
      type: 'number',
      group: 'payment',
      readOnly: true,
      description: 'Otomatik hesaplanan toplam tutar (indirimler dahil)',
      validation: (Rule) => Rule.required().min(0).error('Toplam tutar sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'discountAmount',
      title: 'İndirim Tutarı',
      type: 'number',
      group: 'payment',
      readOnly: true,
      description: 'Kampanya kodundan gelen toplam indirim tutarı',
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'payment',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'paymentStatus',
      title: 'Ödeme Durumu',
      type: 'string',
      group: 'payment',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Ödendi', value: 'paid' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Ödeme durumu zorunludur.'),
    }),
    defineField({
      name: 'paymentIntentId',
      title: 'Ödeme İşlem ID',
      type: 'string',
      group: 'payment',
      description: 'Stripe veya diğer ödeme sisteminden gelen işlem ID’si.',
    }),
    defineField({
      name: 'orderStatus',
      title: 'Sipariş Durumu',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Onaylandı', value: 'confirmed' },
          { title: 'Kargoda', value: 'shipped' },
          { title: 'Teslim Edildi', value: 'delivered' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Sipariş durumu zorunludur.'),
    }),
    defineField({
      name: 'source',
      title: 'Sipariş Kaynağı',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Normal Satın Alma', value: 'regular' },
          { title: 'Çekiliş', value: 'giveaway' },
          { title: 'Açık Artırma', value: 'auction' },
        ],
      },
      initialValue: 'regular',
      validation: (Rule) => Rule.required().error('Sipariş kaynağı zorunludur.'),
    }),
    defineField({
      name: 'campaignCode',
      title: 'Kampanya Kodu',
      type: 'string',
      group: 'payment',
      description: 'Siparişe uygulanan kampanya kodu (varsa).',
    }),
    defineField({
      name: 'createdAt',
      title: 'Oluşturulma Tarihi',
      type: 'datetime',
      group: 'basic',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'updatedAt',
      title: 'Güncellenme Tarihi',
      type: 'datetime',
      group: 'status',
      description: 'Sipariş durumu değiştiğinde otomatik güncellenir',
    }),
    defineAddressField('Teslimat Adresi'),
  ],
  preview: {
    select: {
      title: 'orderNumber',
      customer: 'customer.name',
      totalAmount: 'totalAmount',
      currency: 'currency',
      status: 'orderStatus',
      paymentStatus: 'paymentStatus',
      source: 'source',
    },
    prepare({ title, customer, totalAmount, currency, status, paymentStatus, source }) {
      const statusText = getStatusText(status);
      const paymentText = getPaymentStatusText(paymentStatus);
      const sourceText = getSourceText(source);

      return {
        title: title || 'İsimsiz Sipariş',
        subtitle: `${customer || 'Müşteri Yok'} - ${totalAmount ? `${totalAmount} ${currency || 'TRY'}` : 'Tutar Yok'} - ${statusText} - ${paymentText} - ${sourceText}`,
        media: BasketIcon,
      };
    },
  },
});

// Helper functions for preview
function getStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'Bekliyor',
    confirmed: 'Onaylandı',
    shipped: 'Kargoda',
    delivered: 'Teslim Edildi',
    cancelled: 'İptal',
  };
  return statusMap[status] || status || 'Durum Yok';
}

function getPaymentStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'Ödeme Bekliyor',
    paid: 'Ödendi',
    cancelled: 'Ödeme İptal',
  };
  return statusMap[status] || status || 'Ödeme Durumu Yok';
}

function getSourceText(source: string): string {
  const sourceMap: Record<string, string> = {
    regular: 'Normal',
    giveaway: 'Çekiliş',
    auction: 'Müzayede',
  };
  return sourceMap[source] || source || 'Kaynak Yok';
}
import { BasketIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';

export const orderType = defineType({
  name: 'order',
  title: '<PERSON><PERSON><PERSON><PERSON>',
  type: 'document',
  icon: BasketIcon,
  groups: [
    { name: 'basic', title: '<PERSON>mel Bilgiler' },
    { name: 'items', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { name: 'payment', title: '<PERSON><PERSON><PERSON>' },
    { name: 'status', title: 'Durum' },
  ],
  fields: [
    defineField({
      name: 'orderNumber',
      title: '<PERSON><PERSON><PERSON><PERSON> Numarası',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `ORD-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Sipariş numarası benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'customer',
      title: 'Müşteri',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('Müşteri seçimi zorunludur.'),
    }),
    defineField({
      name: 'orderItems',
      title: 'Sipariş Kalemleri',
      type: 'array',
      group: 'items',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'product',
              title: 'Ürün',
              type: 'reference',
              to: [{ type: 'product' }],
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: 'quantity',
              title: 'Adet',
              type: 'number',
              validation: (Rule) => Rule.required().min(1),
            }),
            defineField({
              name: 'price',
              title: 'Birim Fiyat',
              type: 'number',
              validation: (Rule) => Rule.required().min(0),
            }),
            defineField({
              name: 'currency',
              title: 'Para Birimi',
              type: 'string',
              options: {
                list: [
                  { title: 'Türk Lirası (TRY)', value: 'TRY' },
                  { title: 'ABD Doları (USD)', value: 'USD' },
                  { title: 'Euro (EUR)', value: 'EUR' },
                ],
              },
              initialValue: 'TRY',
              validation: (Rule) => Rule.required(),
            }),
          ],
        }),
      ],
      validation: (Rule) => Rule.min(1).error('En az bir ürün eklenmelidir.'),
    }),
    defineField({
      name: 'totalAmount',
      title: 'Toplam Tutar',
      type: 'number',
      group: 'payment',
      validation: (Rule) => Rule.required().min(0).error('Toplam tutar sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'payment',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'paymentStatus',
      title: 'Ödeme Durumu',
      type: 'string',
      group: 'payment',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Ödendi', value: 'paid' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Ödeme durumu zorunludur.'),
    }),
    defineField({
      name: 'paymentIntentId',
      title: 'Ödeme İşlem ID',
      type: 'string',
      group: 'payment',
      description: 'Stripe veya diğer ödeme sisteminden gelen işlem ID’si.',
    }),
    defineField({
      name: 'orderStatus',
      title: 'Sipariş Durumu',
      type: 'string',
      group: 'status',
      options: {
        list: [
          { title: 'Bekliyor', value: 'pending' },
          { title: 'Onaylandı', value: 'confirmed' },
          { title: 'Kargoda', value: 'shipped' },
          { title: 'Teslim Edildi', value: 'delivered' },
          { title: 'İptal', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required().error('Sipariş durumu zorunludur.'),
    }),
    defineField({
      name: 'source',
      title: 'Sipariş Kaynağı',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Normal Satın Alma', value: 'regular' },
          { title: 'Çekiliş', value: 'giveaway' },
          { title: 'Açık Artırma', value: 'auction' },
        ],
      },
      initialValue: 'regular',
      validation: (Rule) => Rule.required().error('Sipariş kaynağı zorunludur.'),
    }),
    defineField({
      name: 'campaignCode',
      title: 'Kampanya Kodu',
      type: 'string',
      group: 'payment',
      description: 'Siparişe uygulanan kampanya kodu (varsa).',
    }),
    defineField({
      name: 'createdAt',
      title: 'Oluşturulma Tarihi',
      type: 'datetime',
      group: 'basic',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineAddressField('Teslimat Adresi'),
  ],
  preview: {
    select: {
      title: 'orderNumber',
      customer: 'customer.name.tr',
      totalAmount: 'totalAmount',
      currency: 'currency',
      status: 'orderStatus',
    },
    prepare({ title, customer, totalAmount, currency, status }) {
      return {
        title: title || 'İsimsiz Sipariş',
        subtitle: `${customer || 'Müşteri Yok'} - ${totalAmount ? `${totalAmount} ${currency || 'TRY'}` : 'Tutar Yok'} - ${status || 'Durum Yok'}`,
        media: BasketIcon,
      };
    },
  },
});
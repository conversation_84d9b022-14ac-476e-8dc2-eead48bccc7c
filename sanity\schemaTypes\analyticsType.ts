import { ChartBarIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

export const analyticsType = defineType({
  name: 'analytics',
  title: 'Analiz',
  type: 'document',
  icon: ChartBarIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'metrics', title: 'Metrik<PERSON>' },
    { name: 'segmentation', title: 'Segmentasyon' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Analiz ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `ANL-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Analiz ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'type',
      title: '<PERSON><PERSON><PERSON>ü<PERSON>',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Satı<PERSON>', value: 'sales' },
          { title: '<PERSON><PERSON><PERSON>ş', value: 'giveaway' },
          { title: 'Açık Artırma', value: 'auction' },
          { title: 'Kullanıcı Aktivitesi', value: 'user_activity' },
          { title: 'Kampanya Performansı', value: 'campaign' },
        ],
      },
      validation: (Rule) => Rule.required().error('Analiz türü zorunludur.'),
    }),
    defineField({
      name: 'relatedDocument',
      title: 'İlgili Belge',
      type: 'reference',
      to: [
        { type: 'giveaway' },
        { type: 'auction' },
        { type: 'order' },
        { type: 'sales' },
        { type: 'user' },
        { type: 'product' },
        { type: 'category' },
      ],
      group: 'basic',
      description: 'Analizin bağlı olduğu çekiliş, açık artırma, sipariş, kampanya, kullanıcı, ürün veya kategori.',
    }),
    defineField({
      name: 'timeRange',
      title: 'Zaman Aralığı',
      type: 'object',
      group: 'basic',
      fields: [
        defineField({
          name: 'start',
          title: 'Başlangıç',
          type: 'datetime',
          validation: (Rule) => Rule.required().error('Başlangıç zamanı zorunludur.'),
        }),
        defineField({
          name: 'end',
          title: 'Bitiş',
          type: 'datetime',
          validation: (Rule) =>
            Rule.required()
              .min(Rule.valueOfField('start'))
              .error('Bitiş zamanı, başlangıçtan sonra olmalıdır.'),
        }),
      ],
    }),
    defineField({
      name: 'metrics',
      title: 'Metrikler',
      type: 'object',
      group: 'metrics',
      fields: [
        defineField({
          name: 'totalRevenue',
          title: 'Toplam Gelir',
          type: 'number',
          validation: (Rule) => Rule.min(0).error('Toplam gelir sıfır veya pozitif olmalıdır.'),
        }),
        defineField({
          name: 'currency',
          title: 'Para Birimi',
          type: 'string',
          options: {
            list: [
              { title: 'Türk Lirası (TRY)', value: 'TRY' },
              { title: 'ABD Doları (USD)', value: 'USD' },
              { title: 'Euro (EUR)', value: 'EUR' },
            ],
          },
          initialValue: 'TRY',
          validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
        }),
        defineField({
          name: 'participants',
          title: 'Katılımcı Sayısı',
          type: 'number',
          validation: (Rule) => Rule.min(0).error('Katılımcı sayısı sıfır veya pozitif olmalıdır.'),
        }),
        defineField({
          name: 'conversions',
          title: 'Dönüşüm Sayısı',
          type: 'number',
          validation: (Rule) => Rule.min(0).error('Dönüşüm sayısı sıfır veya pozitif olmalıdır.'),
        }),
        defineField({
          name: 'conversionRate',
          title: 'Dönüşüm Oranı (%)',
          type: 'number',
          validation: (Rule) => Rule.min(0).max(100).error('Dönüşüm oranı 0 ile 100 arasında olmalıdır.'),
          description: 'Katılımcıların dönüşüm yüzdesi.',
        }),
        defineField({
          name: 'couponUsage',
          title: 'Kupon Kullanım Sayısı',
          type: 'number',
          validation: (Rule) => Rule.min(0).error('Kupon kullanım sayısı sıfır veya pozitif olmalıdır.'),
          description: 'Kampanya kuponlarının kaç kez kullanıldığı.',
        }),
        defineField({
          name: 'roi',
          title: 'Yatırım Getirisi (ROI)',
          type: 'number',
          description: 'Kampanya veya etkinlik için yatırım getirisi (%).',
        }),
        defineField({
          name: 'socialShares',
          title: 'Sosyal Medya Paylaşımları',
          type: 'number',
          validation: (Rule) => Rule.min(0).error('Paylaşım sayısı sıfır veya pozitif olmalıdır.'),
          description: 'Çekiliş veya açık artırmanın sosyal medyada paylaşılma sayısı.',
        }),
        defineField({
          name: 'timestamp',
          title: 'Zaman Damgası',
          type: 'datetime',
          initialValue: () => new Date().toISOString(),
          validation: (Rule) => Rule.required().error('Zaman damgası zorunludur.'),
        }),
      ],
    }),
    defineField({
      name: 'segmentation',
      title: 'Segmentasyon',
      type: 'object',
      group: 'segmentation',
      fields: [
        defineField({
          name: 'countries',
          title: 'Ülkeler',
          type: 'array',
          of: [{ type: 'string' }],
          description: 'Katılımcıların geldiği ülkeler.',
        }),
        defineField({
          name: 'userRoles',
          title: 'Kullanıcı Rolleri',
          type: 'array',
          of: [
            defineArrayMember({
              type: 'string',
              options: {
                list: [
                  { title: 'Kullanıcı', value: 'user' },
                  { title: 'Moderatör', value: 'moderator' },
                  { title: 'Finans Yöneticisi', value: 'finance_manager' },
                  { title: 'Admin', value: 'admin' },
                ],
              },
            }),
          ],
          description: 'Katılımcıların rolleri.',
        }),
        defineField({
          name: 'loyaltyTiers',
          title: 'Sadakat Seviyeleri',
          type: 'array',
          of: [
            defineArrayMember({
              type: 'string',
              options: {
                list: [
                  { title: 'Standart', value: 'standard' },
                  { title: 'Premium', value: 'premium' },
                  { title: 'VIP', value: 'vip' },
                ],
              },
            }),
          ],
          description: 'Katılımcıların sadakat seviyeleri.',
        }),
      ],
    }),
    defineField({
      name: 'funnel',
      title: 'Dönüşüm Hunisi',
      type: 'array',
      group: 'metrics',
      of: [
        defineArrayMember({
          type: 'object',
          fields: [
            defineField({
              name: 'step',
              title: 'Adım',
              type: 'string',
              options: {
                list: [
                  { title: 'Görüntüleme', value: 'view' },
                  { title: 'Katılım', value: 'participation' },
                  { title: 'Teklif Verme', value: 'bidding' },
                  { title: 'Satın Alma', value: 'purchase' },
                  { title: 'Tamamlama', value: 'completion' },
                ],
              },
              validation: (Rule) => Rule.required().error('Adım zorunludur.'),
            }),
            defineField({
              name: 'count',
              title: 'Adım Sayısı',
              type: 'number',
              validation: (Rule) => Rule.min(0).error('Adım sayısı sıfır veya pozitif olmalıdır.'),
            }),
          ],
        }),
      ],
      description: 'Kullanıcıların etkinlikteki dönüşüm adımları.',
    }),
    defineField({
      name: 'abTest',
      title: 'A/B Testi',
      type: 'object',
      group: 'metrics',
      fields: [
        defineField({
          name: 'variant',
          title: 'Varyant',
          type: 'string',
          description: 'A/B testinin hangi varyantı (örneğin, A veya B).',
        }),
        defineField({
          name: 'performance',
          title: 'Performans',
          type: 'number',
          description: 'Varyantın performansı (örneğin, dönüşüm oranı).',
          validation: (Rule) => Rule.min(0).max(100).error('Performans 0 ile 100 arasında olmalıdır.'),
        }),
      ],
      description: 'A/B testi sonuçları (varsa).',
    }),
  ],
  preview: {
    select: {
      type: 'type',
      revenue: 'metrics.totalRevenue',
      currency: 'metrics.currency',
      timestamp: 'metrics.timestamp',
      participants: 'metrics.participants',
    },
    prepare({ type, revenue, currency, timestamp, participants }) {
      return {
        title: `${type || 'Tür Yok'} Analizi`,
        subtitle: `${revenue ? `${revenue} ${currency || 'TRY'}` : 'Gelir Yok'} - Katılımcılar: ${participants || 0} - ${timestamp ? new Date(timestamp).toLocaleString('tr-TR') : 'Tanımsız'}`,
        media: ChartBarIcon,
      };
    },
  },
});


import { PackageIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Ortak alanlar için yardımcı fonksiyonlar
const defineSlugField = (source = 'name.tr') =>
  defineField({
    name: 'slug',
    title: 'Slug',
    type: 'slug',
    options: { source, maxLength: 96 },
    validation: (Rule) => Rule.required().error('Slug zorunludur.'),
  });

const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin (SEO ve Erişilebilirlik)',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const productType = defineType({
  name: 'product',
  title: 'Ürün',
  type: 'document',
  icon: PackageIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'pricing', title: 'Fiyatlandırma' },
    { name: 'inventory', title: 'Envanter' },
    { name: 'variants', title: 'Varyantlar' },
    { name: 'seo', title: 'SEO' },
    { name: 'flags', title: 'Özellikler' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Ürün ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `PRD-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Ürün ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'name',
      title: 'Ürün Adı',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: 'Ürün Açıklaması',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineSlugField(),
    defineImageField('Ürün Görseli'),
    defineField({
      name: 'category',
      title: 'Ana Kategori',
      type: 'reference',
      to: [{ type: 'category' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('Ana kategori zorunludur.'),
    }),
    defineField({
      name: 'tags',
      title: 'Etiketler',
      type: 'array',
      of: [{ type: 'string' }],
      options: { layout: 'tags' },
      group: 'basic',
      description: 'Ürünü tanımlayan etiketler (örn. yeni, popüler, indirimli).',
    }),
    defineField({
      name: 'price',
      title: 'Fiyat',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.required().min(0).error('Fiyat sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'pricing',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'discount',
      title: 'İndirim Yüzdesi',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.min(0).max(100).error('İndirim 0-100 arasında olmalıdır.'),
      description: 'İndirim yüzdesi (0-100 arası)',
    }),
    defineField({
      name: 'discount',
      title: 'İndirim Yüzdesi',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.min(0).max(100).error('İndirim 0-100 arasında olmalıdır.'),
      description: 'İndirim yüzdesi (0-100 arası)',
    }),
    defineField({
      name: 'sku',
      title: 'SKU',
      type: 'string',
      group: 'inventory',
      readOnly: true,
      initialValue: () => `PROD-${nanoid(6)}`,
      validation: (Rule) => Rule.required().error('SKU zorunludur.'),
    }),
    defineField({
      name: 'stock',
      title: 'Stok Adedi',
      type: 'number',
      group: 'inventory',
      validation: (Rule) => Rule.required().min(0).error('Stok adedi sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'isAvailable',
      title: 'Stokta Var mı?',
      type: 'boolean',
      group: 'inventory',
      initialValue: true,
    }),

    // Varyantlar
    defineField({
      name: 'variants',
      title: 'Ürün Varyantları',
      type: 'array',
      group: 'variants',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'name',
              title: 'Varyant Adı',
              type: 'string',
              validation: (Rule) => Rule.required().error('Varyant adı zorunludur.'),
            },
            {
              name: 'value',
              title: 'Varyant Değeri',
              type: 'string',
              validation: (Rule) => Rule.required().error('Varyant değeri zorunludur.'),
            },
            {
              name: 'stock',
              title: 'Varyant Stok',
              type: 'number',
              validation: (Rule) => Rule.required().min(0).error('Stok sıfır veya pozitif olmalıdır.'),
            },
            {
              name: 'priceModifier',
              title: 'Fiyat Değişikliği',
              type: 'number',
              description: 'Ana fiyata eklenecek/çıkarılacak miktar',
            },
          ],
          preview: {
            select: {
              title: 'name',
              subtitle: 'value',
              stock: 'stock',
            },
            prepare({ title, subtitle, stock }) {
              return {
                title: `${title}: ${subtitle}`,
                subtitle: `Stok: ${stock || 0}`,
              };
            },
          },
        },
      ],
    }),

    // SEO Alanları
    defineField({
      name: 'seoTitle',
      title: 'SEO Başlığı',
      type: 'string',
      group: 'seo',
      validation: (Rule) => Rule.max(60).warning('SEO başlığı 60 karakterden kısa olmalıdır.'),
    }),
    defineField({
      name: 'seoDescription',
      title: 'SEO Açıklaması',
      type: 'text',
      group: 'seo',
      validation: (Rule) => Rule.max(160).warning('SEO açıklaması 160 karakterden kısa olmalıdır.'),
    }),

    // Flags (Özellikler)
    defineField({
      name: 'flags',
      title: 'Ürün Özellikleri',
      type: 'array',
      group: 'flags',
      of: [
        {
          type: 'string',
          options: {
            list: [
              { title: 'Öne Çıkan', value: 'featured' },
              { title: 'Yeni', value: 'new' },
              { title: 'İndirimli', value: 'sale' },
              { title: 'Sınırlı Sayıda', value: 'limited' },
              { title: 'Müzayede', value: 'auction' },
              { title: 'Çekiliş', value: 'giveaway' },
              { title: 'Banner', value: 'banner' },
              { title: 'Ürün Takası', value: 'exchange' },
            ],
          },
        },
      ],
      description: 'Ürünün özel durumlarını belirtir.',
    }),
  ],
  preview: {
    select: {
      title: 'name.tr',
      subtitle: 'price',
      currency: 'currency',
      stock: 'stock',
      media: 'image',
      flags: 'flags',
      discount: 'discount',
    },
    prepare({ title, subtitle, currency, stock, media, flags, discount }) {
      const flagsText = flags && flags.length > 0 ? ` [${flags.join(', ')}]` : '';
      const discountText = discount ? ` (${discount}% indirim)` : '';
      const priceText = subtitle ? `${subtitle} ${currency || 'TRY'}${discountText}` : 'Fiyat Yok';

      return {
        title: title || 'İsimsiz Ürün',
        subtitle: `${priceText} - Stok: ${stock || 0}${flagsText}`,
        media,
      };
    },
  },
});
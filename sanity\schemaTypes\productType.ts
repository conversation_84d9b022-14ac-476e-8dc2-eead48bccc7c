import { PackageIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Ortak alanlar için yardımcı fonksiyonlar
const defineSlugField = (source = 'name.tr') =>
  defineField({
    name: 'slug',
    title: 'Slug',
    type: 'slug',
    options: { source, maxLength: 96 },
    validation: (Rule) => Rule.required().error('Slug zorunludur.'),
  });

const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin (SEO ve Erişilebilirlik)',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

export const productType = defineType({
  name: 'product',
  title: 'Ürün',
  type: 'document',
  icon: PackageIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'pricing', title: 'Fiyatlandırma' },
    { name: 'inventory', title: 'Envanter' },
    { name: 'variants', title: 'Varyantlar' },
    { name: 'seo', title: 'SEO' },
    { name: 'flags', title: 'Özellikler' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Ürün ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `PRD-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Ürün ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'name',
      title: 'Ürün Adı',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: 'Ürün Açıklaması',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineSlugField(),
    defineImageField('Ürün Görseli'),
    defineField({
      name: 'category',
      title: 'Ana Kategori',
      type: 'reference',
      to: [{ type: 'category' }],
      group: 'basic',
      validation: (Rule) => Rule.required().error('Ana kategori zorunludur.'),
    }),
    defineField({
      name: 'tags',
      title: 'Etiketler',
      type: 'array',
      of: [{ type: 'string' }],
      options: { layout: 'tags' },
      group: 'basic',
      description: 'Ürünü tanımlayan etiketler (örn. yeni, popüler, indirimli).',
    }),
    defineField({
      name: 'price',
      title: 'Fiyat',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.required().min(0).error('Fiyat sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'pricing',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'discount',
      title: 'İndirim Yüzdesi',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.min(0).max(100).error('İndirim 0-100 arasında olmalıdır.'),
      description: 'İndirim yüzdesi (0-100 arası)',
    }),
    defineField({
      name: 'stock',
      title: 'Stok Adedi',
      type: 'number',
      group: 'inventory',
      validation: (Rule) => Rule.required().min(0).error('Stok adedi sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'isAvailable',
      title: 'Stokta Var mı?',
      type: 'boolean',
      group: 'inventory',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name.tr',
      subtitle: 'price',
      currency: 'currency',
      stock: 'stock',
      media: 'image',
    },
    prepare({ title, subtitle, currency, stock, media }) {
      return {
        title: title || 'İsimsiz Ürün',
        subtitle: `${subtitle ? `${subtitle} ${currency || 'TRY'}` : 'Fiyat Yok'} - Stok: ${stock || 0}`,
        media,
      };
    },
  },
});
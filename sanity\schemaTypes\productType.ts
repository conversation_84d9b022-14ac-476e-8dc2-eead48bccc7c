import { PackageIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';

export const productType = defineType({
  name: 'product',
  title: 'Ürün',
  type: 'document',
  icon: PackageIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'pricing', title: '<PERSON>yatlandırma' },
    { name: 'inventory', title: 'Envanter' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Ürün ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `PRD-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Ürün ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'name',
      title: 'Ürün <PERSON>',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineImageField('Ürün Görseli'),
    defineField({
      name: 'categories',
      title: 'Kategoriler',
      type: 'array',
      group: 'basic',
      of: [defineArrayMember({ type: 'reference', to: [{ type: 'category' }] })],
    }),
    defineField({
      name: 'price',
      title: 'Fiyat',
      type: 'number',
      group: 'pricing',
      validation: (Rule) => Rule.required().min(0).error('Fiyat sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'pricing',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) => Rule.required().error('Para birimi zorunludur.'),
    }),
    defineField({
      name: 'stock',
      title: 'Stok Adedi',
      type: 'number',
      group: 'inventory',
      validation: (Rule) => Rule.required().min(0).error('Stok adedi sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'isAvailable',
      title: 'Stokta Var mı?',
      type: 'boolean',
      group: 'inventory',
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      title: 'name.tr',
      subtitle: 'price',
      currency: 'currency',
      stock: 'stock',
      media: 'image',
    },
    prepare({ title, subtitle, currency, stock, media }) {
      return {
        title: title || 'İsimsiz Ürün',
        subtitle: `${subtitle ? `${subtitle} ${currency || 'TRY'}` : 'Fiyat Yok'} - Stok: ${stock || 0}`,
        media,
      };
    },
  },
});
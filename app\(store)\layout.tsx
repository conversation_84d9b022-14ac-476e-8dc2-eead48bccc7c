import React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { draftMode } from "next/headers";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";

import "../globals.css";

import Header from "@/components/Header";
import Footer from "@/components/stabilPage/footer";
import { DisableDraftMode } from "@/components/DisableDraftMode";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { isEnabled } = await draftMode();

  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          {isEnabled && <DisableDraftMode />}
          <main>
            <Header />
            {children}
          </main>
          <Footer />
        </body>
      </html>
    </ClerkProvider>
  );
}

import { TagIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';
import { nanoid } from 'nanoid';

// Helper function for image field
const defineImageField = (title = 'Görsel') =>
  defineField({
    name: 'image',
    title,
    type: 'image',
    options: { hotspot: true },
    fields: [
      {
        name: 'alt',
        title: 'Alternatif Metin',
        type: 'string',
        validation: (Rule) => Rule.required().error('Alternatif metin zorunludur.'),
      },
    ],
  });

// Helper function for coupon code field
const defineCouponCodeField = () =>
  defineField({
    name: 'couponCode',
    title: 'Kupon Kodu',
    type: 'string',
    group: 'discount',
    initialValue: () => `COUPON-${nanoid(8).toUpperCase()}`,
    validation: (Rule) =>
      Rule.required()
        .min(3)
        .max(20)
        .regex(/^[A-Z0-9-_]+$/)
        .error('<PERSON>pon kodu zorunludur ve sadece bü<PERSON>ü<PERSON> harf, raka<PERSON>, tire ve alt çizgi içerebilir.'),
    description: 'Müşterilerin kullanacağı kupon kodu. Otomatik oluşturulur ama değiştirilebilir.',
  });

export const salesType = defineType({
  name: 'sales',
  title: 'Kampanya',
  type: 'document',
  icon: TagIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'targets', title: 'Hedef Ürünler/Kategoriler' },
    { name: 'discount', title: 'İndirim Detayları' },
    { name: 'validity', title: 'Geçerlilik' },
    { name: 'usage', title: 'Kullanım Bilgileri' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Kampanya ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `SAL-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Kampanya ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'title',
      title: 'Kampanya Adı',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: 'Kampanya Açıklaması',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineImageField('Kampanya Görseli'),
    defineField({
      name: 'products',
      title: 'Bağlı Ürünler',
      type: 'array',
      group: 'basic',
      of: [defineArrayMember({ type: 'reference', to: [{ type: 'product' }] })],
      description: 'Bu kampanyanın geçerli olduğu ürünler.',
    }),
    defineField({
      name: 'categories',
      title: 'Bağlı Kategoriler',
      type: 'array',
      group: 'basic',
      of: [defineArrayMember({ type: 'reference', to: [{ type: 'category' }] })],
      description: 'Bu kampanyanın geçerli olduğu kategoriler.',
    }),
    defineField({
      name: 'discountType',
      title: 'İndirim Türü',
      type: 'string',
      group: 'discount',
      options: {
        list: [
          { title: 'Yüzde', value: 'percentage' },
          { title: 'Sabit Tutar', value: 'fixed' },
        ],
      },
      initialValue: 'percentage',
      validation: (Rule) => Rule.required().error('İndirim türü zorunludur.'),
    }),
    defineField({
      name: 'discountAmount',
      title: 'İndirim Miktarı',
      type: 'number',
      group: 'discount',
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .custom((value, context) => {
            if (context.document?.discountType === 'percentage' && value > 100) {
              return 'Yüzde indirim 100’den büyük olamaz.';
            }
            return true;
          })
          .error('İndirim miktarı sıfır veya pozitif olmalıdır.'),
    }),
    defineField({
      name: 'currency',
      title: 'Para Birimi',
      type: 'string',
      group: 'discount',
      options: {
        list: [
          { title: 'Türk Lirası (TRY)', value: 'TRY' },
          { title: 'ABD Doları (USD)', value: 'USD' },
          { title: 'Euro (EUR)', value: 'EUR' },
        ],
      },
      initialValue: 'TRY',
      validation: (Rule) =>
        Rule.custom((value, context) => {
          if (context.document?.discountType === 'fixed' && !value) {
            return 'Sabit indirim için para birimi zorunludur.';
          }
          return true;
        }),
    }),
    defineCouponCodeField(),
    defineField({
      name: 'applicableTo',
      title: 'Geçerli Olduğu Alan',
      type: 'string',
      group: 'discount',
      options: {
        list: [
          { title: 'Tümü', value: 'all' },
          { title: 'Çekiliş', value: 'giveaway' },
          { title: 'Açık Artırma', value: 'auction' },
          { title: 'Normal Satın Alma', value: 'regular' },
        ],
      },
      initialValue: 'all',
      validation: (Rule) => Rule.required().error('Geçerli alan zorunludur.'),
    }),
    defineField({
      name: 'validityFrom',
      title: 'Geçerlilik Başlangıcı',
      type: 'datetime',
      group: 'validity',
      validation: (Rule) => Rule.required().error('Geçerlilik başlangıcı zorunludur.'),
    }),
    defineField({
      name: 'validityUntil',
      title: 'Geçerlilik Bitişi',
      type: 'datetime',
      group: 'validity',
      validation: (Rule) =>
        Rule.required()
          .min(Rule.valueOfField('validityFrom'))
          .error('Geçerlilik bitişi, başlangıçtan sonra olmalıdır.'),
    }),
    defineField({
      name: 'isActive',
      title: 'Aktif',
      type: 'boolean',
      group: 'validity',
      initialValue: true,
      description: 'Kampanya şu anda aktif mi?',
    }),
    defineField({
      name: 'usageLimit',
      title: 'Kullanım Sınırı',
      type: 'number',
      group: 'validity',
      validation: (Rule) => Rule.min(0).error('Kullanım sınırı sıfır veya pozitif olmalıdır.'),
      description: 'Kuponun kaç kez kullanılabileceği (0 = sınırsız).',
    }),
    defineField({
      name: 'usedCount',
      title: 'Kullanım Sayısı',
      type: 'number',
      group: 'validity',
      initialValue: 0,
      readOnly: true,
      description: 'Kuponun kaç kez kullanıldığı.',
    }),
    defineField({
      name: 'targetUsers',
      title: 'Hedef Kullanıcılar',
      type: 'array',
      group: 'validity',
      of: [defineArrayMember({ type: 'reference', to: [{ type: 'user' }] })],
      description: 'Bu kampanyanın geçerli olduğu kullanıcılar (boş bırakılırsa herkes için geçerli).',
    }),
  ],
  preview: {
    select: {
      title: 'title.tr',
      discountAmount: 'discountAmount',
      discountType: 'discountType',
      couponCode: 'couponCode',
      isActive: 'isActive',
      validityUntil: 'validityUntil',
      currency: 'currency',
    },
    prepare({ title, discountAmount, discountType, couponCode, isActive, validityUntil, currency }) {
      const status = isActive ? 'Aktif' : 'Pasif';
      const discountText =
        discountType === 'percentage' ? `${discountAmount}%` : `${discountAmount} ${currency || 'TRY'}`;
      return {
        title: title || 'İsimsiz Kampanya',
        subtitle: `${discountText} - Kupon: ${couponCode || 'Yok'} - ${status} - Bitiş: ${
          validityUntil ? new Date(validityUntil).toLocaleDateString('tr-TR') : 'Tanımsız'
        }`,
        media: TagIcon,
      };
    },
  },
});
// lib/client.ts
import { createClient } from 'next-sanity';
import imageUrlBuilder from '@sanity/image-url';
import { apiVersion, dataset, projectId } from '../env';

export const client = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false, // CDN'i devre dışı bırakarak en güncel veriyi alıyoruz
  token: process.env.SANITY_API_TOKEN,
  perspective: 'published',
  ignoreBrowserTokenWarning: true,
  stega: {
    studioUrl: '/Studio',
  },
});

const builder = imageUrlBuilder(client);

export function urlFor(source: any) {
  return builder.image(source).url();
}

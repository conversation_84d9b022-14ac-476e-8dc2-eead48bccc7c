import { BellIcon } from '@sanity/icons';
import { defineArrayMember, defineField, defineType } from 'sanity';

export const notificationType = defineType({
  name: 'notification',
  title: 'Bildirim',
  type: 'document',
  icon: BellIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
    { name: 'content', title: '<PERSON>çeri<PERSON>' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Bildirim ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `NOT-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Bildirim ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'user',
      title: 'Kullanıc<PERSON>',
      type: 'reference',
      to: [{ type: 'user' }],
      group: 'basic',
      description: 'Bildirimin gönderileceği kullanıcı (bo<PERSON> bırakılı<PERSON> tüm kullanıcı<PERSON><PERSON> gö<PERSON>).',
    }),
    defineField({
      name: 'type',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'string',
      group: 'basic',
      options: {
        list: [
          { title: 'Çekiliş Sonucu', value: 'giveaway_result' },
          { title: 'Açık Artırma Güncellemesi', value: 'auction_update' },
          { title: 'Sipariş Durumu', value: 'order_status' },
          { title: 'Cüzdan Yükleme', value: 'wallet_topup' },
          { title: 'Promosyon', value: 'promotion' },
        ],
      },
      validation: (Rule) => Rule.required().error('Bildirim türü zorunludur.'),
    }),
    defineField({
      name: 'title',
      title: 'Bildirim Başlığı',
      type: 'object',
      group: 'content',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'message',
      title: 'Bildirim Mesajı',
      type: 'object',
      group: 'content',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'text' },
      ],
    }),
    defineField({
      name: 'relatedDocument',
      title: 'İlgili Belge',
      type: 'reference',
      to: [{ type: 'giveaway' }, { type: 'auction' }, { type: 'order' }, { type: 'sales' }],
      group: 'basic',
      description: 'Bildirimin ilgili olduğu çekiliş, açık artırma, sipariş veya kampanya.',
    }),
    defineField({
      name: 'sentAt',
      title: 'Gönderim Tarihi',
      type: 'datetime',
      group: 'basic',
      initialValue: () => new Date().toISOString(),
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'isRead',
      title: 'Okundu mu?',
      type: 'boolean',
      group: 'basic',
      initialValue: false,
    }),
  ],
  preview: {
    select: {
      title: 'title.tr',
      type: 'type',
      user: 'user.name.tr',
      sentAt: 'sentAt',
    },
    prepare({ title, type, user, sentAt }) {
      return {
        title: title || 'İsimsiz Bildirim',
        subtitle: `${type || 'Tür Yok'} - ${user || 'Tüm Kullanıcılar'} - ${sentAt ? new Date(sentAt).toLocaleString('tr-TR') : 'Tanımsız'}`,
        media: BellIcon,
      };
    },
  },
});
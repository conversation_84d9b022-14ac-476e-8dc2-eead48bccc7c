import { type SchemaTypeDefinition } from 'sanity'

import {blockContentType} from './blockContentType'
import {categoryType} from './categoryType'
import {productType} from './productType'
import {salesType} from './salesType'
import {orderType} from './orderType'
import {userType} from './userType'
import {Auction} from './auctionType'
import {giveawayType} from './giveawayType'
import {walletTransactionType} from './walletTransactionType'
import { walletTopUpRequestType } from "./walletTopUpRequestType";


export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    blockContentType,
    categoryType,
    productType,
    salesType,
    orderType,
    userType,
    Auction,
    giveawayType,
    walletTransactionType,
    walletTopUpRequestType
  ],
}

import { CategoryIcon } from '@sanity/icons';
import { defineField, defineType } from 'sanity';

export const categoryType = defineType({
  name: 'category',
  title: 'Kategori',
  type: 'document',
  icon: CategoryIcon,
  groups: [
    { name: 'basic', title: 'Temel Bilgiler' },
  ],
  fields: [
    defineField({
      name: 'id',
      title: 'Kategori ID',
      type: 'string',
      group: 'basic',
      readOnly: true,
      initialValue: () => `CAT-${nanoid(8)}`,
      validation: (Rule) => Rule.required().unique().error('Kategori ID benzersiz olmalıdır.'),
    }),
    defineField({
      name: 'name',
      title: 'Kategori Adı',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'string', validation: (Rule) => Rule.required() },
        { name: 'en', title: 'İngilizce', type: 'string' },
      ],
    }),
    defineField({
      name: 'description',
      title: '<PERSON><PERSON><PERSON>',
      type: 'object',
      group: 'basic',
      fields: [
        { name: 'tr', title: 'Türkçe', type: 'text' },
        { name: 'en', title: '<PERSON>ng<PERSON><PERSON><PERSON>', type: 'text' },
      ],
    }),
    defineImageField('Kategori Görseli'),
  ],
  preview: {
    select: {
      title: 'name.tr',
      media: 'image',
    },
    prepare({ title, media }) {
      return {
        title: title || 'İsimsiz Kategori',
        media,
      };
    },
  },
});
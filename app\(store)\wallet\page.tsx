import { currentUser } from '@clerk/nextjs/server';
import { getUserByClerkId } from '@/sanity/lib/users/getUser';
import { getWalletTopUpRequestsByUser } from '@/sanity/lib/users/getWalletTopUpRequests';
import WalletClient from '@/components/WalletClient';
import { client } from '@/sanity/lib/client';

export default async function WalletPage() {
  const clerkUser = await currentUser();
  if (!clerkUser || !clerkUser.id || !clerkUser.emailAddresses[0]?.emailAddress) {
    return <div className="p-8 text-center"><PERSON><PERSON><PERSON> yapmalısınız.</div>;
  }
  const user = await getUserByClerkId({
    id: clerkUser.id,
    email: clerkUser.emailAddresses[0].emailAddress,
    name: clerkUser.fullName || clerkUser.firstName || clerkUser.emailAddresses[0].emailAddress,
  });
  const requests = await getWalletTopUpRequestsByUser(user?._id || '');
  if (!user) {
    return <div className="p-8 text-center text-red-600">Kullanı<PERSON>ı bulunamadı veya giriş yapılmadı.</div>;
  }
  if (!requests) {
    return <div className="p-8 text-center text-red-600">Yükleme talepleri alınamadı.</div>;
  }

  // Kullanıcının biletlerini çek
  const giveaways = await client.fetch(`*[_type == "giveaway" && count(participants[user._ref == $userId]) > 0]{
    _id,
    title,
    status,
    ticketPrice,
    numbersPerCard,
    winningNumbers,
    winners,
    participants[user._ref == $userId]{
      tickets[] {
        ticketNumber,
        purchasedAt,
        chosenDigitCount
      }
    }
  }`, { userId: user._id });

  // Her çekiliş için kartları grupla (her kart: numbersPerCard adet numara)
  let groupedCards = giveaways.flatMap((giveaway: any) => {
    const numbersPerCard = giveaway.numbersPerCard || 3;
    // Tüm biletleri satın alma tarihine göre sırala (yeni en başta)
    const allTickets = (giveaway.participants || []).flatMap((p: any) => p.tickets || []);
    allTickets.sort((a: any, b: any) => new Date(b.purchasedAt).getTime() - new Date(a.purchasedAt).getTime());
    // Kartlara böl
    const cards = [];
    for (let i = 0; i < allTickets.length; i += numbersPerCard) {
      const cardTickets = allTickets.slice(i, i + numbersPerCard);
      if (cardTickets.length === numbersPerCard) {
        // Kazanan kontrolü
        const isWinner = (giveaway.winningNumbers || []).some((num: string) => cardTickets.map((t: any) => t.ticketNumber).includes(num));
        cards.push({
          giveawayTitle: giveaway.title,
          giveawayId: giveaway._id,
          status: giveaway.status,
          ticketPrice: giveaway.ticketPrice,
          numbers: cardTickets.map((t: any) => t.ticketNumber),
          purchasedAt: cardTickets[0].purchasedAt, // ilk numaranın tarihi (en yeni kart en üstte)
          winningNumbers: giveaway.winningNumbers || [],
          winners: giveaway.winners || [],
          isWinner,
        });
      }
    }
    return cards;
  });
  // Kazanan kartlar en üstte olacak şekilde sırala
  groupedCards = groupedCards.sort((a, b) => (b.isWinner ? 1 : 0) - (a.isWinner ? 1 : 0));

  return <WalletClient user={user} requests={requests} cards={groupedCards} />;
} 
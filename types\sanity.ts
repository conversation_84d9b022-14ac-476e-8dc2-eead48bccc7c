// Enhanced TypeScript interfaces for Sanity CMS with multilingual support

export interface MultilingualText {
  tr: string;
  en?: string;
}

export interface MultilingualTextArea {
  tr?: string;
  en?: string;
}

export interface SanityImage {
  asset: {
    _ref: string;
    _type: 'reference';
  };
  alt: string;
  hotspot?: {
    x: number;
    y: number;
    height: number;
    width: number;
  };
  crop?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface SanitySlug {
  current: string;
  _type: 'slug';
}

export interface ProductVariant {
  name: string;
  value: string;
  stock: number;
  priceModifier?: number;
}

export interface Product {
  _id: string;
  _type: 'product';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  id: string;
  name: MultilingualText;
  slug: SanitySlug;
  image: SanityImage;
  description: MultilingualTextArea;
  category: {
    _ref: string;
    _type: 'reference';
  };
  tags?: string[];
  price: number;
  currency: string;
  discount?: number;
  sku: string;
  stock: number;
  isAvailable: boolean;
  variants?: ProductVariant[];
  flags?: ('featured' | 'new' | 'sale' | 'limited' | 'auction' | 'giveaway' | 'banner' | 'exchange')[];
  seoTitle?: string;
  seoDescription?: string;
}

export interface Category {
  _id: string;
  _type: 'category';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: MultilingualText;
  slug: SanitySlug;
  image?: SanityImage;
  description?: MultilingualTextArea;
  parentCategory?: {
    _ref: string;
    _type: 'reference';
  };
  tags?: string[];
  flags?: ('featured' | 'seasonal' | 'promotion' | 'new')[];
  seoTitle?: string;
  seoDescription?: string;
}

// Populated versions (with resolved references)
export interface ProductWithCategory extends Omit<Product, 'category'> {
  category: Category;
}

export interface CategoryWithParent extends Omit<Category, 'parentCategory'> {
  parentCategory?: Category;
}

// API Response types
export interface ProductsResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
}

export interface CategoriesResponse {
  categories: Category[];
  total: number;
}

// Query parameters
export interface ProductQueryParams {
  category?: string;
  tags?: string[];
  flags?: string[];
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'createdAt' | 'stock';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

export interface CategoryQueryParams {
  parentCategory?: string;
  flags?: string[];
  language?: 'tr' | 'en';
}

// Form types for creating/updating
export interface CreateProductData {
  name: MultilingualText;
  description: MultilingualTextArea;
  category: string; // category ID
  tags?: string[];
  price: number;
  currency: string;
  discount?: number;
  stock: number;
  variants?: Omit<ProductVariant, 'stock'>[];
  flags?: Product['flags'];
  seoTitle?: string;
  seoDescription?: string;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface CreateCategoryData {
  title: MultilingualText;
  description?: MultilingualTextArea;
  parentCategory?: string; // category ID
  tags?: string[];
  flags?: Category['flags'];
  seoTitle?: string;
  seoDescription?: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string;
}

// Utility types
export type ProductFlag = Product['flags'][number];
export type CategoryFlag = Category['flags'][number];
export type Currency = 'TRY' | 'USD' | 'EUR';
export type Language = 'tr' | 'en';

// Helper type for getting localized text
export type LocalizedText<T extends MultilingualText | MultilingualTextArea> = 
  T extends MultilingualText ? string : string | undefined;

// Error types
export interface SanityError {
  message: string;
  code?: string;
  details?: any;
}

export interface ApiResponse<T> {
  data?: T;
  error?: SanityError;
  success: boolean;
}

// Enhanced TypeScript interfaces for Sanity CMS with multilingual support

export interface MultilingualText {
  tr: string;
  en?: string;
}

export interface MultilingualTextArea {
  tr?: string;
  en?: string;
}

export interface SanityImage {
  asset: {
    _ref: string;
    _type: 'reference';
  };
  alt: string;
  hotspot?: {
    x: number;
    y: number;
    height: number;
    width: number;
  };
  crop?: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface SanitySlug {
  current: string;
  _type: 'slug';
}

export interface ProductVariant {
  name: string;
  value: string;
  stock: number;
  priceModifier?: number;
}

export interface Product {
  _id: string;
  _type: 'product';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  id: string;
  name: MultilingualText;
  slug: SanitySlug;
  image: SanityImage;
  description: MultilingualTextArea;
  category: {
    _ref: string;
    _type: 'reference';
  };
  tags?: string[];
  price: number;
  currency: string;
  discount?: number;
  sku: string;
  stock: number;
  isAvailable: boolean;
  variants?: ProductVariant[];
  flags?: ('featured' | 'new' | 'sale' | 'limited' | 'auction' | 'giveaway' | 'banner' | 'exchange')[];
  seoTitle?: string;
  seoDescription?: string;
}

export interface Category {
  _id: string;
  _type: 'category';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title: MultilingualText;
  slug: SanitySlug;
  image?: SanityImage;
  description?: MultilingualTextArea;
  parentCategory?: {
    _ref: string;
    _type: 'reference';
  };
  tags?: string[];
  flags?: ('featured' | 'seasonal' | 'promotion' | 'new')[];
  seoTitle?: string;
  seoDescription?: string;
}

// Populated versions (with resolved references)
export interface ProductWithCategory extends Omit<Product, 'category'> {
  category: Category;
}

export interface CategoryWithParent extends Omit<Category, 'parentCategory'> {
  parentCategory?: Category;
}

// API Response types
export interface ProductsResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
}

export interface CategoriesResponse {
  categories: Category[];
  total: number;
}

// Query parameters
export interface ProductQueryParams {
  category?: string;
  tags?: string[];
  flags?: string[];
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'createdAt' | 'stock';
  sortOrder?: 'asc' | 'desc';
  language?: 'tr' | 'en';
}

export interface CategoryQueryParams {
  parentCategory?: string;
  flags?: string[];
  language?: 'tr' | 'en';
}

// Form types for creating/updating
export interface CreateProductData {
  name: MultilingualText;
  description: MultilingualTextArea;
  category: string; // category ID
  tags?: string[];
  price: number;
  currency: string;
  discount?: number;
  stock: number;
  variants?: Omit<ProductVariant, 'stock'>[];
  flags?: Product['flags'];
  seoTitle?: string;
  seoDescription?: string;
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string;
}

export interface CreateCategoryData {
  title: MultilingualText;
  description?: MultilingualTextArea;
  parentCategory?: string; // category ID
  tags?: string[];
  flags?: Category['flags'];
  seoTitle?: string;
  seoDescription?: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string;
}

// Utility types
export type ProductFlag = Product['flags'][number];
export type CategoryFlag = Category['flags'][number];
export type Currency = 'TRY' | 'USD' | 'EUR';
export type Language = 'tr' | 'en';

// Order interfaces
export interface OrderItem {
  product: SanityReference;
  quantity: number;
  price: number;
  currency: Currency;
  discount?: number;
}

export interface CustomerInfo {
  name: string;
  email: string;
  clerkUserId: string;
}

export interface ShippingAddress {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
}

export interface Order {
  _id: string;
  _type: 'order';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderNumber: string;
  customer: CustomerInfo;
  orderNotes?: MultilingualTextArea;
  orderItems: OrderItem[];
  totalAmount: number;
  discountAmount?: number;
  currency: Currency;
  paymentStatus: 'pending' | 'paid' | 'cancelled';
  paymentIntentId?: string;
  orderStatus: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  source: 'regular' | 'giveaway' | 'auction';
  campaignCode?: string;
  shippingAddress: ShippingAddress;
  createdAt: string;
  updatedAt?: string;
}

// User interfaces
export interface UserAddress {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
}

export interface User {
  _id: string;
  _type: 'user';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  clerkId: string;
  name: MultilingualText;
  email: string;
  image?: SanityImage;
  phoneNumber?: string;
  addresses: UserAddress[];
  status: 'active' | 'inactive' | 'suspended';
  lastLogin?: string;
  walletBalance: number;
  currency: Currency;
  isAdminApproved: boolean;
  role: 'user' | 'moderator' | 'finance_manager' | 'admin';
}

// Sales interfaces
export interface Sale {
  _id: string;
  _type: 'sales';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  id: string;
  title: MultilingualText;
  description?: MultilingualTextArea;
  image?: SanityImage;
  products?: SanityReference[];
  categories?: SanityReference[];
  discountType: 'percentage' | 'fixed_amount';
  discountAmount: number;
  currency: Currency;
  couponCode: string;
  validityFrom: string;
  validityUntil: string;
  isActive: boolean;
  usageLimit?: number;
  usedCount: number;
  targetUsers?: SanityReference[];
}

// Wallet Transaction interfaces
export interface WalletTransaction {
  _id: string;
  _type: 'walletTransaction';
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  transactionId: string;
  user: SanityReference;
  amount: number;
  currency: Currency;
  type: 'deposit' | 'withdrawal' | 'refund' | 'bonus' | 'commission' | 'penalty';
  description?: string;
  relatedOrder?: SanityReference;
  relatedTopUpRequest?: SanityReference;
  status: 'pending' | 'completed' | 'cancelled' | 'failed';
  balanceBefore?: number;
  balanceAfter?: number;
  createdAt: string;
  processedAt?: string;
  processedBy?: SanityReference;
  notes?: string;
}

// Populated versions (with resolved references)
export interface OrderWithDetails extends Omit<Order, 'orderItems'> {
  orderItems: (Omit<OrderItem, 'product'> & { product: Product })[];
}

export interface UserWithDetails extends User {
  // Add any additional populated fields if needed
}

export interface SaleWithDetails extends Omit<Sale, 'products' | 'categories' | 'targetUsers'> {
  products?: Product[];
  categories?: Category[];
  targetUsers?: User[];
}

export interface WalletTransactionWithDetails extends Omit<WalletTransaction, 'user' | 'relatedOrder' | 'relatedTopUpRequest' | 'processedBy'> {
  user: User;
  relatedOrder?: Order;
  relatedTopUpRequest?: any; // Define WalletTopUpRequest interface if needed
  processedBy?: User;
}

// Helper type for getting localized text
export type LocalizedText<T extends MultilingualText | MultilingualTextArea> =
  T extends MultilingualText ? string : string | undefined;

// Error types
export interface SanityError {
  message: string;
  code?: string;
  details?: any;
}

export interface ApiResponse<T> {
  data?: T;
  error?: SanityError;
  success: boolean;
}
